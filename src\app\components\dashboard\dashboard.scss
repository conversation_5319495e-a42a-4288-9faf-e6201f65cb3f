.dashboard-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.dashboard-header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 20px 0;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h1 {
      margin: 0;
      color: #333;
      font-size: 24px;
      font-weight: 600;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
}

.filters-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  gap: 16px;
  align-items: center;

  .search-box {
    flex: 1;

    input {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 16px;

      &:focus {
        outline: none;
        border-color: #667eea;
      }
    }
  }

  .type-filter {
    select {
      padding: 12px 16px;
      border: 1px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      background: white;

      &:focus {
        outline: none;
        border-color: #667eea;
      }
    }
  }
}

.credentials-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 40px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.credential-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }

  &.api-key {
    border-left: 4px solid #28a745;
  }

  &.password {
    border-left: 4px solid #007bff;
  }
}

.card-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  .card-actions {
    display: flex;
    gap: 8px;
  }
}

.card-content {
  padding: 16px 20px;

  .credential-info {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;

    .type-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;

      &.password {
        background: #e3f2fd;
        color: #1976d2;
      }

      &.api_key {
        background: #e8f5e8;
        color: #2e7d32;
      }

      &.token {
        background: #fff3e0;
        color: #f57c00;
      }
    }

    .category {
      color: #666;
      font-size: 14px;
    }
  }
}

.field {
  margin-bottom: 12px;

  label {
    display: block;
    font-weight: 500;
    color: #555;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .field-value {
    display: flex;
    align-items: center;
    gap: 8px;

    span {
      flex: 1;
      font-family: monospace;
      background: #f8f9fa;
      padding: 8px;
      border-radius: 4px;
      font-size: 14px;
    }

    a {
      flex: 1;
      color: #667eea;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .notes {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
  }
}

.api-status {
  margin-top: 8px;

  .status-indicator {
    font-size: 12px;
    font-weight: 600;

    &.valid {
      color: #28a745;
    }

    &.invalid {
      color: #dc3545;
    }
  }

  small {
    display: block;
    color: #666;
    margin-top: 4px;
  }
}

.tags {
  margin-top: 12px;

  .tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 8px;
    margin-bottom: 4px;
  }
}

.card-footer {
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;

  small {
    color: #666;
    font-size: 12px;
  }
}

// Buttons
.btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a6fd8;
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: #5a6268;
    }
  }
}

.action-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background: #f8f9fa;
  }

  &.delete:hover {
    background: #f8d7da;
  }
}

.copy-btn, .validate-btn {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background: #e9ecef;
  }
}

.empty-state {
  max-width: 1200px;
  margin: 0 auto;
  padding: 60px 20px;
  text-align: center;

  .empty-content {
    max-width: 400px;
    margin: 0 auto;

    h3 {
      color: #666;
      margin-bottom: 16px;
    }

    p {
      color: #888;
      margin-bottom: 24px;
    }
  }
}