import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { CredentialService } from '../../services/credential.service';
import { Credential, CredentialType } from '../../models/credential.model';

@Component({
  selector: 'app-dashboard',
  imports: [CommonModule, FormsModule],
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.scss'
})
export class Dashboard implements OnInit {
  private authService = inject(AuthService);
  private credentialService = inject(CredentialService);
  private router = inject(Router);

  credentials = signal<Credential[]>([]);
  filteredCredentials = signal<Credential[]>([]);
  searchTerm = signal('');
  selectedType = signal<CredentialType | 'all'>('all');
  showPassword = signal<{[key: string]: boolean}>({});

  credentialTypes = Object.values(CredentialType);

  ngOnInit() {
    this.loadCredentials();
  }

  loadCredentials() {
    this.credentialService.getUserCredentials().subscribe(creds => {
      this.credentials.set(creds);
      this.filterCredentials();
    });
  }

  filterCredentials() {
    let filtered = this.credentials();

    if (this.searchTerm()) {
      filtered = filtered.filter(cred =>
        cred.title.toLowerCase().includes(this.searchTerm().toLowerCase()) ||
        cred.category.toLowerCase().includes(this.searchTerm().toLowerCase())
      );
    }

    if (this.selectedType() !== 'all') {
      filtered = filtered.filter(cred => cred.type === this.selectedType());
    }

    this.filteredCredentials.set(filtered);
  }

  onSearchChange(term: string) {
    this.searchTerm.set(term);
    this.filterCredentials();
  }

  onTypeChange(type: CredentialType | 'all') {
    this.selectedType.set(type);
    this.filterCredentials();
  }

  togglePasswordVisibility(credentialId: string) {
    const current = this.showPassword();
    this.showPassword.set({
      ...current,
      [credentialId]: !current[credentialId]
    });
  }

  async copyToClipboard(text: string) {
    try {
      await this.credentialService.copyToClipboard(text);
      // You can add a toast notification here
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }

  async validateApiKey(credential: Credential) {
    if (credential.apiKey) {
      const result = await this.credentialService.validateApiKey(credential.apiKey, credential.category);
      await this.credentialService.updateCredential(credential.id!, {
        isValid: result.isValid,
        lastValidated: result.lastChecked
      });
      this.loadCredentials();
    }
  }

  addNewCredential() {
    this.router.navigate(['/add-credential']);
  }

  editCredential(id: string) {
    this.router.navigate(['/edit-credential', id]);
  }

  async deleteCredential(id: string) {
    if (confirm('Are you sure you want to delete this credential?')) {
      await this.credentialService.deleteCredential(id);
      this.loadCredentials();
    }
  }

  async signOut() {
    await this.authService.signOut();
  }
}
