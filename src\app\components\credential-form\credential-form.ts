import { Component, inject, OnInit } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { CredentialService } from '../../services/credential.service';
import { Credential, CredentialType } from '../../models/credential.model';

@Component({
  selector: 'app-credential-form',
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './credential-form.html',
  styleUrl: './credential-form.scss'
})
export class CredentialForm implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private credentialService = inject(CredentialService);

  credentialForm: FormGroup;
  isEditMode = false;
  credentialId: string | null = null;
  loading = false;
  error = '';

  credentialTypes = Object.values(CredentialType);

  predefinedCategories = [
    'Social Media',
    'Email',
    'Banking',
    'Work',
    'Development',
    'AI Services',
    'Cloud Services',
    'Entertainment',
    'Shopping',
    'Other'
  ];

  constructor() {
    this.credentialForm = this.fb.group({
      title: ['', [Validators.required]],
      type: [CredentialType.PASSWORD, [Validators.required]],
      category: ['', [Validators.required]],
      username: [''],
      password: [''],
      apiKey: [''],
      url: [''],
      notes: [''],
      tags: ['']
    });
  }

  ngOnInit() {
    this.credentialId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.credentialId;

    if (this.isEditMode) {
      this.loadCredential();
    }

    // Watch for type changes to show/hide relevant fields
    this.credentialForm.get('type')?.valueChanges.subscribe(type => {
      this.updateValidators(type);
    });
  }

  updateValidators(type: CredentialType) {
    const passwordControl = this.credentialForm.get('password');
    const apiKeyControl = this.credentialForm.get('apiKey');

    // Clear existing validators
    passwordControl?.clearValidators();
    apiKeyControl?.clearValidators();

    // Add validators based on type
    if (type === CredentialType.PASSWORD) {
      passwordControl?.setValidators([Validators.required]);
    } else if (type === CredentialType.API_KEY || type === CredentialType.TOKEN) {
      apiKeyControl?.setValidators([Validators.required]);
    }

    passwordControl?.updateValueAndValidity();
    apiKeyControl?.updateValueAndValidity();
  }

  async loadCredential() {
    if (!this.credentialId) return;

    try {
      // In a real app, you'd have a method to get a single credential
      // For now, we'll get all and find the one we need
      this.credentialService.getUserCredentials().subscribe(credentials => {
        const credential = credentials.find(c => c.id === this.credentialId);
        if (credential) {
          this.credentialForm.patchValue({
            title: credential.title,
            type: credential.type,
            category: credential.category,
            username: credential.username || '',
            password: credential.password || '',
            apiKey: credential.apiKey || '',
            url: credential.url || '',
            notes: credential.notes || '',
            tags: credential.tags?.join(', ') || ''
          });
        }
      });
    } catch (error) {
      this.error = 'Failed to load credential';
    }
  }

  async onSubmit() {
    if (this.credentialForm.valid) {
      this.loading = true;
      this.error = '';

      try {
        const formValue = this.credentialForm.value;
        const credentialData = {
          ...formValue,
          tags: formValue.tags ? formValue.tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag) : []
        };

        if (this.isEditMode && this.credentialId) {
          await this.credentialService.updateCredential(this.credentialId, credentialData);
        } else {
          await this.credentialService.addCredential(credentialData);
        }

        this.router.navigate(['/dashboard']);
      } catch (error: any) {
        this.error = error.message || 'Failed to save credential';
      } finally {
        this.loading = false;
      }
    }
  }

  cancel() {
    this.router.navigate(['/dashboard']);
  }

  get isPasswordType() {
    return this.credentialForm.get('type')?.value === CredentialType.PASSWORD;
  }

  get isApiKeyType() {
    return this.credentialForm.get('type')?.value === CredentialType.API_KEY ||
           this.credentialForm.get('type')?.value === CredentialType.TOKEN;
  }
}
