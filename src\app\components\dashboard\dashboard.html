<div class="dashboard-container">
  <!-- Header -->
  <header class="dashboard-header">
    <div class="header-content">
      <h1>🔐 Credential Manager</h1>
      <div class="header-actions">
        <button class="btn btn-primary" (click)="addNewCredential()">
          ➕ Add New
        </button>
        <button class="btn btn-secondary" (click)="signOut()">
          🚪 Sign Out
        </button>
      </div>
    </div>
  </header>

  <!-- Filters -->
  <div class="filters-section">
    <div class="search-box">
      <input
        type="text"
        placeholder="🔍 Search credentials..."
        [value]="searchTerm()"
        (input)="onSearchChange($event.target.value)">
    </div>

    <div class="type-filter">
      <select [value]="selectedType()" (change)="onTypeChange($event.target.value)">
        <option value="all">All Types</option>
        <option *ngFor="let type of credentialTypes" [value]="type">
          {{ type | titlecase }}
        </option>
      </select>
    </div>
  </div>

  <!-- Credentials Grid -->
  <div class="credentials-grid">
    <div
      *ngFor="let credential of filteredCredentials()"
      class="credential-card"
      [class.api-key]="credential.type === 'api_key'"
      [class.password]="credential.type === 'password'">

      <div class="card-header">
        <h3>{{ credential.title }}</h3>
        <div class="card-actions">
          <button class="action-btn" (click)="editCredential(credential.id!)" title="Edit">
            ✏️
          </button>
          <button class="action-btn delete" (click)="deleteCredential(credential.id!)" title="Delete">
            🗑️
          </button>
        </div>
      </div>

      <div class="card-content">
        <div class="credential-info">
          <span class="type-badge" [class]="credential.type">
            {{ credential.type | titlecase }}
          </span>
          <span class="category">{{ credential.category }}</span>
        </div>

        <!-- Username -->
        <div *ngIf="credential.username" class="field">
          <label>Username:</label>
          <div class="field-value">
            <span>{{ credential.username }}</span>
            <button class="copy-btn" (click)="copyToClipboard(credential.username!)" title="Copy">
              📋
            </button>
          </div>
        </div>

        <!-- Password -->
        <div *ngIf="credential.password" class="field">
          <label>Password:</label>
          <div class="field-value">
            <span>{{ showPassword()[credential.id!] ? credential.password : '••••••••' }}</span>
            <button class="copy-btn" (click)="togglePasswordVisibility(credential.id!)" title="Toggle visibility">
              {{ showPassword()[credential.id!] ? '🙈' : '👁️' }}
            </button>
            <button class="copy-btn" (click)="copyToClipboard(credential.password!)" title="Copy">
              📋
            </button>
          </div>
        </div>

        <!-- API Key -->
        <div *ngIf="credential.apiKey" class="field">
          <label>API Key:</label>
          <div class="field-value">
            <span>{{ showPassword()[credential.id!] ? credential.apiKey : '••••••••••••••••' }}</span>
            <button class="copy-btn" (click)="togglePasswordVisibility(credential.id!)" title="Toggle visibility">
              {{ showPassword()[credential.id!] ? '🙈' : '👁️' }}
            </button>
            <button class="copy-btn" (click)="copyToClipboard(credential.apiKey!)" title="Copy">
              📋
            </button>
            <button class="validate-btn" (click)="validateApiKey(credential)" title="Validate API Key">
              🔍
            </button>
          </div>

          <!-- API Key Status -->
          <div class="api-status" *ngIf="credential.lastValidated">
            <span
              class="status-indicator"
              [class.valid]="credential.isValid"
              [class.invalid]="!credential.isValid">
              {{ credential.isValid ? '✅ Valid' : '❌ Invalid' }}
            </span>
            <small>Last checked: {{ credential.lastValidated | date:'short' }}</small>
          </div>
        </div>

        <!-- URL -->
        <div *ngIf="credential.url" class="field">
          <label>URL:</label>
          <div class="field-value">
            <a [href]="credential.url" target="_blank">{{ credential.url }}</a>
            <button class="copy-btn" (click)="copyToClipboard(credential.url!)" title="Copy">
              📋
            </button>
          </div>
        </div>

        <!-- Notes -->
        <div *ngIf="credential.notes" class="field">
          <label>Notes:</label>
          <p class="notes">{{ credential.notes }}</p>
        </div>

        <!-- Tags -->
        <div *ngIf="credential.tags && credential.tags.length > 0" class="tags">
          <span *ngFor="let tag of credential.tags" class="tag">{{ tag }}</span>
        </div>
      </div>

      <div class="card-footer">
        <small>Updated: {{ credential.updatedAt | date:'short' }}</small>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="filteredCredentials().length === 0" class="empty-state">
    <div class="empty-content">
      <h3>🔍 No credentials found</h3>
      <p *ngIf="searchTerm() || selectedType() !== 'all'">
        Try adjusting your search or filter criteria.
      </p>
      <p *ngIf="!searchTerm() && selectedType() === 'all'">
        Start by adding your first credential!
      </p>
      <button class="btn btn-primary" (click)="addNewCredential()">
        ➕ Add Your First Credential
      </button>
    </div>
  </div>
</div>
