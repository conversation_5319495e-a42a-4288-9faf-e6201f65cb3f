import { Injectable, inject } from '@angular/core';
import { Firestore, collection, addDoc, updateDoc, deleteDoc, doc, getDocs, query, where, orderBy } from '@angular/fire/firestore';
import { Observable, from, map } from 'rxjs';
import { Credential, CredentialType, ValidationResult } from '../models/credential.model';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CredentialService {
  private firestore = inject(Firestore);
  private authService = inject(AuthService);
  private credentialsCollection = collection(this.firestore, 'credentials');

  async addCredential(credential: Omit<Credential, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const userId = this.authService.getCurrentUserId();
    if (!userId) throw new Error('User not authenticated');

    const newCredential: Omit<Credential, 'id'> = {
      ...credential,
      userId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const docRef = await addDoc(this.credentialsCollection, newCredential);
    return docRef.id;
  }

  async updateCredential(id: string, updates: Partial<Credential>): Promise<void> {
    const credentialDoc = doc(this.firestore, 'credentials', id);
    await updateDoc(credentialDoc, {
      ...updates,
      updatedAt: new Date()
    });
  }

  async deleteCredential(id: string): Promise<void> {
    const credentialDoc = doc(this.firestore, 'credentials', id);
    await deleteDoc(credentialDoc);
  }

  getUserCredentials(): Observable<Credential[]> {
    const userId = this.authService.getCurrentUserId();
    if (!userId) return from([]);

    const q = query(
      this.credentialsCollection,
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );

    return from(getDocs(q)).pipe(
      map(snapshot => 
        snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Credential))
      )
    );
  }

  async validateApiKey(apiKey: string, type: string): Promise<ValidationResult> {
    // Basic validation logic - you can extend this based on specific API requirements
    try {
      let isValid = false;
      let message = '';

      // Example validation for different API types
      switch (type.toLowerCase()) {
        case 'openai':
          isValid = apiKey.startsWith('sk-') && apiKey.length > 20;
          message = isValid ? 'Valid OpenAI API key format' : 'Invalid OpenAI API key format';
          break;
        case 'google':
          isValid = apiKey.length === 39;
          message = isValid ? 'Valid Google API key format' : 'Invalid Google API key format';
          break;
        default:
          isValid = apiKey.length > 10;
          message = isValid ? 'API key format appears valid' : 'API key too short';
      }

      return {
        isValid,
        message,
        lastChecked: new Date()
      };
    } catch (error) {
      return {
        isValid: false,
        message: 'Validation failed',
        lastChecked: new Date()
      };
    }
  }

  // Simple encryption/decryption (for demo purposes - use proper encryption in production)
  private encrypt(text: string): string {
    return btoa(text); // Base64 encoding (not secure for production)
  }

  private decrypt(encryptedText: string): string {
    return atob(encryptedText); // Base64 decoding
  }

  copyToClipboard(text: string): Promise<void> {
    return navigator.clipboard.writeText(text);
  }
}
