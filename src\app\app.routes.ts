import { Routes } from '@angular/router';
import { Login } from './components/login/login';
import { Dashboard } from './components/dashboard/dashboard';
import { CredentialForm } from './components/credential-form/credential-form';
import { inject } from '@angular/core';
import { AuthService } from './services/auth.service';

// Auth Guard function
const authGuard = () => {
  const authService = inject(AuthService);
  if (authService.isAuthenticated()) {
    return true;
  } else {
    return ['/login'];
  }
};

// Redirect authenticated users away from login
const loginGuard = () => {
  const authService = inject(AuthService);
  if (authService.isAuthenticated()) {
    return ['/dashboard'];
  } else {
    return true;
  }
};

export const routes: Routes = [
  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
  { path: 'login', component: Login, canActivate: [loginGuard] },
  { path: 'dashboard', component: Dashboard, canActivate: [authGuard] },
  { path: 'add-credential', component: CredentialForm, canActivate: [authGuard] },
  { path: 'edit-credential/:id', component: CredentialForm, canActivate: [authGuard] },
  { path: '**', redirectTo: '/dashboard' }
];
